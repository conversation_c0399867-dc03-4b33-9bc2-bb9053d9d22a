name: Android QA6 Build

on:
  push:
    branches: [qa6] 

jobs:
  build-android:
    runs-on: ubuntu-latest
    env:
      BUILD_ENV: "qa6"
      VERSION_SUFFIX: "-beta.${{ github.run_number }}"

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

      - name: Install dependencies
        run: npm install

      - name: Build Staging APK
        run: |
          npm run build -- --configuration=qa6
          npx cap sync android
          cd android
          ./gradlew assembleDebug

      - name: Upload Staging APK
        uses: actions/upload-artifact@v4
        with:
          name: app-qa6-debug
          path: android/app/build/outputs/apk/debug/*.apk
