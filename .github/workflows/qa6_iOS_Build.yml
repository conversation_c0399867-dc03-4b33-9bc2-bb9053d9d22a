name: iOS QA6 Build

on:
  push:
    branches: [main, auto-build-qa6]
  workflow_dispatch:

env:
  NODE_VERSION: '20.x'
  XCODE_VERSION: '15.2'
  SCHEME: 'App'
  BUNDLE_ID: 'com.citus.stage'
  DEVELOPMENT_TEAM: '8LE2DAQG25'
  PROVISIONING_PROFILE: 'CitusHealth Stage Provisioning'

jobs:
  build-ios:
    runs-on: macos-latest
    env:
      BUILD_ENV: "qa6"
      VERSION_SUFFIX: "-beta.${{ github.run_number }}"

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          scope: '@citushealth-inc'
          registry-url: 'https://npm.pkg.github.com'

      - name: Install dependencies
        run: npm install --force
        env:
          NODE_AUTH_TOKEN: ${{ secrets.CITUS_NPM_AUTH_TOKEN }}

      - name: Decode and install certificate
        run: |
          echo "${{ secrets.IOS_CERTIFICATE_BASE64 }}" | base64 --decode > 13-6cert.p12
          security create-keychain -p "" build.keychain
          security import 13-6cert.p12 -k build.keychain -P "${{ secrets.IOS_CERTIFICATE_PASSWORD }}" -A
          security list-keychains -s build.keychain
          security unlock-keychain -p "" build.keychain
          security set-key-partition-list -S apple-tool:,apple: -s -k "" build.keychain

      - name: Decode and install provisioning profile
        run: |
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo "${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision

      - name: Build iOS App
        run: |
          npm run build -- --configuration=qa6
          npx cap sync ios
          cd ios/App
          mkdir -p build
          xcodebuild \
            -workspace App.xcworkspace \
            -scheme "${{ env.SCHEME }}" \
            -configuration Release \
            -archivePath $PWD/build/App.xcarchive \
            DEVELOPMENT_TEAM=${{ env.DEVELOPMENT_TEAM }} \
            PRODUCT_BUNDLE_IDENTIFIER=${{ env.BUNDLE_ID }} \
            PROVISIONING_PROFILE_SPECIFIER="${{ env.PROVISIONING_PROFILE }}" \
            archive

      - name: Export IPA
        run: |
          cd ios/App
          xcodebuild -exportArchive \
            -archivePath build/App.xcarchive \
            -exportOptionsPlist ../exportOptions.plist \
            -exportPath build

      - name: Upload IPA as artifact
        uses: actions/upload-artifact@v4
        with:
          name: ios-qa6-build
          path: ios/App/build/*.ipa
