{"name": "AutoBuild_DemoApp", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^20.0.4", "@angular/common": "^20.0.4", "@angular/compiler": "^20.0.4", "@angular/core": "^20.0.4", "@angular/forms": "^20.0.4", "@angular/platform-browser": "^20.0.4", "@angular/platform-browser-dynamic": "^20.0.4", "@angular/router": "^20.0.4", "@capacitor/android": "^7.4.0", "@capacitor/app": "7.0.1", "@capacitor/core": "7.4.0", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "7.4.0", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/angular": "^8.6.2", "ionicons": "^8.0.9", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.3", "@angular-eslint/builder": "^20.1.1", "@angular-eslint/eslint-plugin": "^20.1.1", "@angular-eslint/eslint-plugin-template": "^20.1.1", "@angular-eslint/schematics": "^20.1.1", "@angular-eslint/template-parser": "^20.1.1", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.4", "@angular/language-service": "^20.0.4", "@capacitor/cli": "7.4.0", "@ionic/angular-toolkit": "^12.2.0", "@types/jasmine": "~5.1.8", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^51.0.3", "eslint-plugin-prefer-arrow": "1.2.3", "jasmine-core": "~5.8.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}, "description": "An Ionic project"}